<template>
    <tui-bottom-popup :show="show" @close="handleClose" :z-index="1000" >
        <view class="shopInfo-popup">
            <!-- 头部显示商品信息 -->
            <view class="shopInfo-popup-header">
                <view class="picture-wrapper">
                    <image :src="chooseGoods.image" mode="widthFix"></image>
                </view>
                <view class="good-info-wrapper">
                    <view>
                        <text class="price-symbol">￥</text>
                        <text class="price-value">{{ chooseGoods.price }}</text>
                    </view>
                    <view class="good-info-wrapper-bottom">
                        <text class="price-confirm">已选</text>
                        <text class="price-name">{{ chooseGoods.name }}</text>
                    </view>
                </view>
            </view>
            <!-- 商品列表展示部分 -->
            <view class="shopInfo-popup-list">
                <view class="showInfo-popup-list-header">
                    <text class="header-text">规格</text>
                    <view class="showInfo-popup-list-header-right" @click="handleChangeListType">
                        <image v-if="listType === 'grid'" src="/static/images/order-addcart/list.svg" mode="widthFix">
                        </image>
                        <image v-else src="/static/images/order-addcart/grid-list.svg" mode="widthFix"></image>
                        <text class="list-text" v-if="listType === 'grid'">列表</text>
                        <text class="list-text" v-else>大图</text>
                    </view>
                </view>
                <!-- 使用 scroll-view 组件确保滚动功能正常 -->
                <scroll-view class="list-scroll-container" :class="listType" :scroll-x="listType === 'grid'"
                    :scroll-y="listType === 'list'" :enable-flex="true" :show-scrollbar="false">
                    <view class="list-content" :class="listType">
                        <view class="list-content-item" v-for="(item, index) in list" :key="item.id"
                            :class="{ active: activeIndex === index }" @click="handleClick(index)">
                            <view class="list-content-item-top">
                                <image :src="item.image" mode="widthFix"></image>
                            </view>
                            <text class="list-content-item-bottom">
                                {{ item.name }}
                            </text>
                        </view>
                    </view>
                </scroll-view>
                <!-- 外观列表 -->
                <list-of-popup
                    :list="appearanceList"
                    title="外观"
                    :default-active-index="activeAppearanceIndex"
                    @change="handleAppearanceChange"
                    ref="appearanceListRef"
                ></list-of-popup>
                <!-- 版本列表 -->
                <list-of-popup
                    :list="versionList"
                    title="版本"
                    :default-active-index="activeVersionIndex"
                    @change="handleVersionChange"
                    ref="versionListRef"
                ></list-of-popup>
            </view>
            <view class="shopInfo-popup-footer">
                <view class="shopInfo-popup-footer-left">数量</view>
                <view class="shopInfo-popup-footer-right">
                    <view class="reduce" @click.stop='subCart'>-</view>
                    <input class='num ml-8 mr-8' type="number" v-model="num" @input="onNumInput" :maxlength="3"
                        confirm-type="done" placeholder="1" />
                    <view class="plus" @click.stop='addCart'>+</view>
                </view>
            </view>
            <view class="confirm-btn">
                确定
            </view>
        </view>
    </tui-bottom-popup>
</template>

<script>
import tuiBottomPopup from '@/components/base/tui-bottom-popup.vue';
import listOfPopup from './list-of-popup.vue';
export default {
    components: {
        tuiBottomPopup,
        listOfPopup
    },
    emits: ['close'],
    data() {
        return {
            appearanceList: [
                {
                    id: 1,
                    name: '青海湖',
                },
                {
                    id: 2,
                    name: '雅典黑',
                },
                {
                    id: 3,
                    name: '香槟金',
                },
            ],
            versionList: [
                {
                    id: 1,
                    name: '8GB+128GB',
                },
            ],
            list: [
                {
                    id: 1,
                    name: '原味燕窝胶原饮90g',
                    price: 164.00,
                    image: 'https://weipinshang.oss-cn-shenzhen.aliyuncs.com/78c09202504081350441652.jpg'
                },
                {
                    id: 2,
                    name: '原味燕窝胶原饮94g',
                    price: 200.00,
                    image: 'https://weipinshang.oss-cn-shenzhen.aliyuncs.com/78c09202504081350441652.jpg'
                },
                {
                    id: 3,
                    name: '原味燕窝胶原饮190g',
                    price: 300.00,
                    image: 'https://weipinshang.oss-cn-shenzhen.aliyuncs.com/78c09202504081350441652.jpg'
                },
                {
                    id: 4,
                    name: '原味燕窝胶原饮90g',
                    price: 164.00,
                    image: 'https://weipinshang.oss-cn-shenzhen.aliyuncs.com/78c09202504081350441652.jpg'
                },
                {
                    id: 5,
                    name: '原味燕窝胶原饮90g',
                    price: 164.00,
                    image: 'https://weipinshang.oss-cn-shenzhen.aliyuncs.com/78c09202504081350441652.jpg'
                },
                {
                    id: 6,
                    name: '原味燕窝胶原饮90g',
                    price: 164.00,
                    image: 'https://weipinshang.oss-cn-shenzhen.aliyuncs.com/78c09202504081350441652.jpg'
                },
                {
                    id: 7,
                    name: '原味燕窝胶原饮90g',
                    price: 164.00,
                    image: 'https://weipinshang.oss-cn-shenzhen.aliyuncs.com/78c09202504081350441652.jpg'
                },
                {
                    id: 8,
                    name: '原味燕窝胶原饮90g',
                    price: 164.00,
                    image: 'https://weipinshang.oss-cn-shenzhen.aliyuncs.com/78c09202504081350441652.jpg'
                }
            ],
            activeIndex: 0,
            listType: 'grid',
            num: 1,
            activeAppearanceIndex: 0,
        }
    },
    computed: {
        chooseGoods() {
            return this.list[this.activeIndex];
        }
    },
    props: {
        shopInfo: {
            type: Object,
            default: () => { }
        },
        show: {
            type: Boolean,
            default: false
        }
    },
    methods: {
        handleAppearanceChange(data) {
            this.activeAppearanceIndex = data.index;
            console.log('外观选择变更:', data);
        },
        handleChangeListType() {
            this.listType = this.listType === 'grid' ? 'list' : 'grid';
        },
        handleClose() {
            this.$emit('close');
        },
        handleClick(index) {
            this.activeIndex = index;
        },
        subCart() {
            console.log("subCart")
            if (this.num > 1) {
                this.num--;
            }
        },
        addCart() {
            console.log("addCart")
            this.num++;
        },
        onNumInput(e) {
            // 获取输入值
            let value = e.detail.value;

            // 确保输入的是正整数
            if (value === '' || value === '0') {
                this.num = 1;
            } else {
                // 转换为数字并确保是正整数
                let numValue = parseInt(value);
                if (isNaN(numValue) || numValue < 1) {
                    this.num = 1;
                } else {
                    this.num = numValue;
                }
            }
        }
    }
}
</script>

<style lang="scss" scoped>
@import "../index.scss";

.shopInfo-popup {
    z-index: 1000;
    height: 100%;
    padding-top: 60rpx;
    padding-left: 30rpx;
    padding-right: 30rpx;
    min-height: 557rpx * 2;
    padding-bottom: 98rpx;
    display: flex;
    flex-direction: column;
}

// header
.shopInfo-popup-header {
    display: flex;
    align-items: center;
    gap: 14rpx;
    margin-bottom: 46rpx;
}

.good-info-wrapper {
    align-self: flex-end;
    display: flex;
    flex-direction: column;
    gap: 12rpx;

    .price-confirm {
        color: #666;
        font-size: 24rpx;
        font-weight: 400;
    }

    .price-symbol {
        color: #ff0000;
        font-size: 28rpx;
        font-weight: 600;
        font-family: 'PingFang SC', sans-serif;
    }

    .price-value {
        color: #ff0000;
        font-size: 40rpx;
        font-weight: 600;
        font-family: 'PingFang SC', sans-serif;
    }

    .good-info-wrapper-bottom {
        display: flex;
        gap: 12rpx;
        align-items: center;
    }
}

// 商品列表展示部分
.shopInfo-popup-list {
    display: flex;
    flex: 1;
    overflow: hidden;
    overflow-y: auto;
    flex-direction: column;
    gap: 24rpx;
}

.showInfo-popup-list-header {
    display: flex;
    justify-content: space-between;

    .showInfo-popup-list-header-right {
        display: flex;
        align-items: center;
        gap: 8rpx;

        image {
            width: 32rpx;
            height: 32rpx;
        }

        text {
            color: #131313;
            font-size: 24rpx;
            font-weight: 400;
        }
    }
}

.header-text {
    color: #131313;
    font-size: 28rpx;
    font-weight: 500;
}

// 滚动容器样式
.list-scroll-container {
    border-radius: 16rpx;

    // Grid布局：横向滚动
    &.grid {
        height: 280rpx; // 设置固定高度
        // white-space: nowrap;
    }

    // List布局：纵向滚动
    &.list {
        max-height: 280rpx; // 限制最大高度，大约4行的高度
    }
}

.list-content {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20rpx;
    border-radius: 16rpx;
    transition: all 0.1s cubic-bezier(0.4, 0, 0.2, 1); // 添加平滑过渡
    will-change: transform; // 优化动画性能

    // Grid布局：一行三列，超出部分横向滚动
    &.grid {
        display: flex;
        flex-direction: row;
        gap: 20rpx;
        height: 100%;

        .list-content-item {
            flex: 0 0 calc(33.333% - 14rpx); // 固定宽度，一行显示3个
            min-width: calc(33.333% - 14rpx);
            max-width: calc(33.333% - 14rpx);
        }
    }
}

.list-content-item {
    display: flex;
    flex-direction: column;
    border-radius: 16rpx;
    min-height: 0;
    border: 2px solid transparent; // 添加透明边框，避免布局跳动
    transition: border-color 0.3s ease; // 只对边框颜色做过渡
    background-color: #f7f7f7;


    &.active {
        border-color: #F00; // 只改变边框颜色，不改变边框宽度

        .list-content-item-bottom {
            color: #F00;
            transition: color 0.3s ease; // 文字颜色过渡
        }
    }

    .list-content-item-top {
        width: 100%;
        // height: 220rpx;
        overflow: hidden; // 添加 overflow: hidden 来裁剪超出部分
        border-top-left-radius: 16rpx;
        border-top-right-radius: 16rpx;

        image {
            width: 100%;
            height: 100%;

            object-fit: cover; // 添加 object-fit: cover 来保持比例并填充容器
        }
    }

    .list-content-item-bottom {
        flex: 1;
        padding: 8rpx;
        color: #131313;
        font-size: 24rpx;
        font-weight: 400;
        text-align: left;
    }
}

// list 样式
.list-content.list {
    display: flex;
    flex-direction: column;
    gap: 16rpx;
    height: 100%; // 使用100%高度，让scroll-view控制滚动

    .list-content-item {
        background-color: #f7f7f7;
        display: flex;
        flex-direction: row;
        align-items: center;
        gap: 12rpx;
        width: 100%;
        padding: 2rpx 4rpx;
        min-height: 80rpx; // 确保每行有固定高度
        flex-shrink: 0; // 防止项目被压缩
    }

    .list-content-item-top {
        width: 64rpx;
        height: 64rpx;
        border-top-left-radius: 16rpx;
        border-bottom-left-radius: 16rpx;
        border-top-right-radius: 0;
        border-bottom-right-radius: 0;
        overflow: hidden; // 添加 overflow: hidden 来裁剪超出部分
    }
}

.shopInfo-popup-footer {
    display: flex;
    justify-content: space-between;
    margin: 28rpx 0;

    .shopInfo-popup-footer-left {
        color: #131313;
        font-size: 28rpx;
        font-weight: 500;
    }

    .shopInfo-popup-footer-right {
        display: flex;
        align-items: center;
        gap: 12rpx;

        .reduce,
        .plus {
            width: 50rpx;
            height: 50rpx;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: #f7f7f7;
            border-radius: 8rpx;
            color: #131313;
            font-size: 28rpx;
            font-weight: 500;
        }

        .num {
            width: 60rpx;
            height: 40rpx;
            text-align: center;
            border: 1px solid #e5e5e5;
            border-radius: 8rpx;
            background-color: #fff;
            font-size: 28rpx;
            color: #131313;
        }
    }
}

.confirm-btn {
    border-radius: 48rpx;
    background: #F00;
    width: 100%;
    color: #FFF;
    font-size: 32rpx;
    font-weight: 500;
    text-align: center;
    padding: 16rpx 0;
    margin-top: auto;
    margin-bottom: 32rpx;
    // align-self: flex-end;
    // position: fixed;
    /* 或者使用 relative */
    // bottom: 200rpx;
    // left: 30rpx;
    // right: 30rpx;
    // width: auto;
    /* 让left和right控制宽度 */
}


</style>